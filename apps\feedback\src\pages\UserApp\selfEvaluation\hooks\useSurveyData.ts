import { useState, useCallback } from 'react';
import {
  getSelfEvaluationSurveyData,
  type SelfEvaluationSurveyData,
  type SurveySection
} from '@/services/selfEvaluationService';

export const useSurveyData = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [surveyData, setSurveyData] = useState<SelfEvaluationSurveyData | null>(null);
  const [currentSection, setCurrentSection] = useState<SurveySection | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchSurveyData = useCallback(async (surveyId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!surveyId) {
        throw new Error('Survey ID is required');
      }

      const data = await getSelfEvaluationSurveyData(surveyId);
      setSurveyData(data);

      // Set the first section as current
      if (data.sections.length > 0) {
        setCurrentSection(data.sections[0]);
      }
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetchSurveyData = useCallback(async () => {
    if (surveyData) {
      // Re-fetch to get updated completion percentage
      try {
        const data = await getSelfEvaluationSurveyData(surveyData.responseId);
        setSurveyData(data);
      } catch (err) {
        console.error('Error refetching survey data:', err);
      }
    }
  }, [surveyData]);

  return {
    isLoading,
    surveyData,
    currentSection,
    error,
    setCurrentSection,
    setError,
    fetchSurveyData,
    refetchSurveyData
  };
};
