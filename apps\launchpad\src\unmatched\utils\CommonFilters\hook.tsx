import React from "react";
import { useDispatch, useSelector } from "../../hooks";
import util from "../../utils";
import { getMetaLabelsFact } from "./filters-api";
// import { ErrorType } from "unmatched/types";
import actions from "./slice";
import _ from "lodash";

export default function useFilter(id?: any) {
  const filters: Array<any> = useSelector(
    (state: any) => state.filters.filters
  );
  const [selected, setSelected] = React.useState<any>({});
  const dispatch = useDispatch();

  const fetchFilters = (cb?: Function) => {
    getMetaLabelsFact({ survey_id: id }).then((response: any) => {
      dispatch(actions.set(response));
      if (cb) cb(response);
    });
  };

  const getFilters = (cb?: Function) => {
    if (!_.isEmpty(filters)) {
      if (cb) cb(filters);
    } else {
      fetchFilters(cb);
    }
  };

  const onSelect = (item: any) => {
    console.log(item);
    setSelected(item);
  };

  const getParams: any = (_selected: any) => {
    let params = {};
    Object.keys(_selected).forEach((key: string) => {
      const values = _.get(_selected, key);
      if (!values.length) return;
      params = { ...params, [key]: util.getCommaSeperatedFromArray(values) };
    });
    return params;
  };

  return {
    filters,
    selected,
    getFilters,
    onSelect,
    getParams,
  };
}
