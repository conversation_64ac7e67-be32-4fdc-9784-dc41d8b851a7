import axios, { AxiosError, AxiosResponse } from "axios";
// AxiosRequestConfig,
// import logger from "./logger";
// import session from "./session";
// import { util } from "@unmatchedoffl/ui-core";
import log from "./logger";
import session from "./session";

// const { log, session } = util;

// const requestInterceptor = (request: AxiosRequestConfig) => {
//   const token = session.getToken();
//   if (token) {
//     return {
//       ...request,
//       headers: {
//         ...request.headers,
//         Authorization: `Token ${token}`,
//       },
//     };
//   }
//   return request;
// };

const responseInterceptor = (response: AxiosResponse) => {
  log.success(response);
  return response;
};

const errorResponseInterceptor = (error: AxiosError) => {
  const errorConfig = {
    msg: "There is some server issue.",
    statusCode: 502,
    reason: {},
    // requestUrl: "",
  };
  if (error.response && error.response.data as any) {
    // errorConfig.requestUrl = error.response.config.url || "";
    errorConfig.msg =
      (error?.response?.data as any)?.detail ||
      (error?.response?.data as any)?.message ||
      (error?.response?.data as any)?.reason ||
      (error?.response?.data as any)?.error;
    errorConfig.statusCode = error.response.status || errorConfig.statusCode;
    if ((error?.response?.data as any)?.reason) {
      errorConfig.reason = (error?.response?.data as any)?.reason;
    }
  } else if (error.message) {
    errorConfig.msg = error.message;
  }
  log.error(error);
  return errorConfig;
};

const init = (baseURL: string) => {
  axios.defaults.baseURL = baseURL || ""; //eslint-disable-line
};

const addInterceptors = () => {
  axios.interceptors.response.use(responseInterceptor, (err: any) => {
    const { statusCode, msg, reason } = errorResponseInterceptor(err);
    // const errMsg = util.getMeaningfullMsg(msg);
    if ([401, "401"].includes(statusCode)) {
      log.error("Unauthorized", msg);
      session.onUnAuthorize();
    }
    return Promise.reject({ statusCode, msg, reason, data: err.response.data });
  });

  // axios.interceptors.request.use((request: AxiosRequestConfig) =>
  //   requestInterceptor(request)
  // );
};

const getConfigurations = (_params: any, _meta: any) => {
  const params = _params || {};
  const meta = _meta || {};
  return {
    params,
    ...meta,
  };
};

const api = {
  init,
  addInterceptors,
  getConfigurations,
};

export default api;
