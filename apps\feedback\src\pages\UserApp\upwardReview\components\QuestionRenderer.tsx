import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { Input } from '@repo/ui/components/input';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { Badge } from '@repo/ui/components/badge';
import { Separator } from '@repo/ui/components/separator';
import { MessageSquare } from 'lucide-react';

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
  options?: any[];
  response?: any;
}

interface QuestionRendererProps {
  question: Question;
  questionNumber: number;
  onUpdate: (response: any, feedback?: string) => void;
  isSaving: boolean;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onUpdate,
  isSaving
}) => {
  const [response, setResponse] = useState(question.response || '');
  const [feedback, setFeedback] = useState(question.feedback || '');
  const [showFeedback, setShowFeedback] = useState(!!question.feedback);

  const handleResponseChange = (newResponse: any) => {
    setResponse(newResponse);
    onUpdate(newResponse, feedback);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    onUpdate(response, newFeedback);
  };

  const renderRatingScale = () => {
    // Get the options from the question or use default scale format
    const options = question.options || {
      "1": "Scale - 1",
      "2": "Scale - 2",
      "3": "Scale - 3",
      "4": "Scale - 4",
      "5": "Scale - 5"
    };

    // Create ratings array starting from 5 down to 1, plus "No basis" option
    const ratings = [
      { value: 5, label: options["5"] || 'Scale - 5' },
      { value: 4, label: options["4"] || 'Scale - 4' },
      { value: 3, label: options["3"] || 'Scale - 3' },
      { value: 2, label: options["2"] || 'Scale - 2' },
      { value: 1, label: options["1"] || 'Scale - 1' },
      { value: 0, label: 'No basis for rating' }
    ];

    return (
      <div className="mt-4">
        <div className="grid grid-cols-6 gap-3 w-full">
          {ratings.map((rating) => (
            <div key={rating.value} className="flex flex-col">
              <Button
                type="button"
                variant={response === rating.value ? "default" : "outline"}
                onClick={() => handleResponseChange(rating.value)}
                className={`w-full h-auto p-0 border transition-colors ${
                  response === rating.value
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'
                }`}
              >
                <div className="flex items-center w-full">
                  <div className="flex items-center justify-center min-w-[30px] py-2 px-2 font-medium">
                    {rating.value}
                  </div>
                  <div className="border-l border-gray-300 px-3 py-2 flex-1 text-left text-sm">
                    {rating.value === 0 ? 'No basis for rating' : rating.label}
                  </div>
                </div>
              </Button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options) return null;

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={(value) => handleResponseChange(value)}
        className="space-y-3"
      >
        {question.options.map((option) => (
          <div key={option.id} className="flex items-center space-x-3">
            <RadioGroupItem value={option.value?.toString() || option.text} id={`option-${option.id}`} />
            <Label htmlFor={`option-${option.id}`} className="cursor-pointer">
              {option.text}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option) => (
          <div key={option.id} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id}`}
              checked={selectedValues.includes(option.value || option.text)}
              onCheckedChange={(checked) => {
                const newValues = checked
                  ? [...selectedValues, option.value || option.text]
                  : selectedValues.filter(v => v !== (option.value || option.text));
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id}`} className="cursor-pointer">
              {option.text}
            </Label>
          </div>
        ))}
      </div>
    );
  };

  const renderTextarea = () => (
    <Textarea
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Please provide your response..."
      className="min-h-[120px] resize-none"
    />
  );

  const renderInput = () => (
    <Input
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
    />
  );

  const renderRanking = () => {
    if (!question.options) return null;

    return (
      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Drag and drop to rank these items from most important (1) to least important ({question.options.length})
        </p>
        <div className="space-y-2">
          {question.options.map((option, index) => (
            <div
              key={option.id}
              className="flex items-center space-x-3 p-3 border rounded-lg bg-card"
            >
              <Badge variant="outline">{index + 1}</Badge>
              <span>{option.text}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderQuestionContent = () => {
    switch (question.type.toLowerCase()) {
      case 'rating':
      case 'scale':
        return renderRatingScale();
      case 'radio':
      case 'single_choice':
        return renderRadioOptions();
      case 'checkbox':
      case 'multiple_choice':
        return renderCheckboxOptions();
      case 'textarea':
      case 'paragraph':
      case 'long_text':
        return renderTextarea();
      case 'input':
      case 'text':
      case 'short_text':
        return renderInput();
      case 'ranking':
        return renderRanking();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.type}" not yet implemented
          </div>
        );
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Badge variant="outline">Q{questionNumber}</Badge>
              {question.hasFeedback && (
                <Badge variant="secondary" className="text-xs">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  Feedback Optional
                </Badge>
              )}
            </div>
            <CardTitle className="text-lg leading-relaxed">
              {question.title}
            </CardTitle>
          </div>
          {isSaving && (
            <Badge variant="outline" className="animate-pulse">
              Saving...
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Question Response */}
        <div className="space-y-4">
          {renderQuestionContent()}
        </div>

        {/* Feedback Section */}
        {question.hasFeedback && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Additional Comments (Optional)
                </Label>
                {!showFeedback && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFeedback(true)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Comment
                  </Button>
                )}
              </div>
              
              {showFeedback && (
                <div className="space-y-3">
                  <Textarea
                    value={feedback}
                    onChange={(e) => handleFeedbackChange(e.target.value)}
                    placeholder="Please provide any additional comments or feedback..."
                    className="min-h-[80px] resize-none"
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowFeedback(false);
                        setFeedback('');
                        handleFeedbackChange('');
                      }}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionRenderer;
