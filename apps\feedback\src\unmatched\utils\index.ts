// import assets from "../../assets";
import urls from "./urls";
import canAccess from "./permissions";
// import api from "./api";
import userAgent from "./browser";
// import { util } from "@unmatchedoffl/ui-core";
import * as enums from "./enums";
import { DateTime } from "luxon";
import assets from "@/assets";
import session from "./session";

const utilModule = {
  // ...util,
  ...assets,
  ...urls,
  // api,
  canAccess,
  userAgent,
  enums,
  session,
  env: {
    isProd: true,
  },
  date: {
    getDateAfter: (count: number) => {
      const now = new Date();
      now.setDate(now.getDate() + count);
      return now;
    },
    getTimeStamp: () => {
      return new Date().getTime();
    },
    getTimeStampString: () => {
      return new Date().getTime().toString();
    },
    getFormatedTime: (time: any, format?: string) => {
      return DateTime.fromJSDate(time).toFormat(format || " MMMM dd, yyyy");
    },
    getBrowserTime: (time: any, format?: string) => {
      return DateTime.fromISO(time).toFormat(format || " MMMM dd, yyyy");
    },
    getISOString: (val: any) => {
      if (val) new Date(val).toISOString();
      return "";
    },
    getDate: (val: any) => {
      const date = val.getDate();
      const month = val.getMonth();
      const year = val.getFullYear();
      return `${year}-${month + 1}-${date}`;
    },
  },
  label: {
    getSortingLabel: (key: string, type: string) => {
      return type === enums.Sort.Ascending ? key : `-${key}`;
    },
  },
  math: {
    roundOff: (num: number, toHigh?: boolean, toLow?: boolean) => {
      if (toHigh) {
        return Math.ceil(num);
      } else if (toLow) {
        return Math.floor(num);
      }
      return Math.round(num);
    },
    isEven: (num: number) => num % 2 === 0 || num === 0,
    getRamdomNumber: () => +Math.random().toString().split(".")[1],
  },
  deepCheck: (obj1: any, obj2: any) => {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  },
  getFullName({ firstName, lastName, email }: any) {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    return email || "";
  },
  getCommaSeperatedFromArray: (arr: Array<string>) => {
    return arr.join(",");
  },
  getContentFromBrackets: (item: any) => {
    const txt = item || "";
    const re = /\((.*)\)/;
    return txt.match(re)[1] || "";
  },
  preventEnterKey: (e: any) => {
    const isEnter = e.key === "Enter";
    if (isEnter) {
      e.preventDefault();
    }
    return isEnter;
  },
  preventAndStopEvent: (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  },
  filterRow: (r: any, propsToSearch: Array<string>, search: string) =>
    propsToSearch.some((c) => {
      const s = search?.toLowerCase();
      if (Array.isArray(r[c])) {
        return r[c].some((el: any) => el?.toLowerCase()?.includes(s));
      }
      return `${r[c]}`.toLowerCase?.().includes?.(s.toLowerCase?.());
    }),
  enableDisableTree: (tree: any = [], disabled = false) => {
    for (let i = 0; i < tree.length; i++) {
      if (tree[i]) {
        (tree[i] as any).disabled = disabled;
      }
    }
  },
  splitArrayToChunks(array: any, parts: any) {
    const result: any = [];
    for (let i = parts; i > 0; i--) {
      result.push(array.splice(0, Math.ceil(array.length / i)));
    }
    return result;
  },
  getQuestionNumbers(questions: any) {
    const output: any = {};
    let count = 1;
    questions.forEach((item: any) => {
      if (item.type === enums.QUESTION.Paragraph) return;
      output[item.id] = count;
      count += 1;
    });
    return output;
  },
  noSearchRecordsFoundMsg: "Please check and try again with a different keyword, or filters."
};

export default utilModule;
